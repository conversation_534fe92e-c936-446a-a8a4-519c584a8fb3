# 心流锚定系统 - 技术需求文档

**版本**: 1.0  
**更新日期**: 2024-12-19  
**文档类型**: 技术需求规格说明

## 1. 概述

### 1.1 文档目的
本文档详细描述心流锚定系统的技术功能需求，为开发团队提供明确的技术实现指导。

### 1.2 产品定位
面向易分心用户的智能专注力管理桌面应用，通过结构化引导、智能辅助和持续反馈，帮助用户克服分心、锚定专注、高效达成目标。

### 1.3 技术原则
- **用户隐私优先**: 敏感数据本地存储，明确告知权限用途
- **性能优化**: 低资源占用，快速响应
- **渐进增强**: 核心功能优先，高级功能可选
- **跨平台兼容**: 支持主流桌面操作系统

## 2. 功能需求规格

### 2.1 目标输入与解析模块 (Goal Intake & Interpretation)

#### FR-GI-001: 结构化目标输入
**优先级**: P0 (MVP必需)

**功能描述**:
用户通过结构化表单创建和管理目标

**技术规格**:
```typescript
interface Goal {
  id: string;
  userId: string;
  name: string; // 最大50字符
  description: string; // 富文本支持
  type: 'long-term' | 'short-term' | 'habit';
  whyPower: string; // 核心驱动力，最少50字符
  domains: string[]; // 关联领域标签
  startDate?: Date;
  deadline?: Date;
  status: 'active' | 'completed' | 'paused' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}
```

**验收标准**:
- [ ] 表单验证：必填字段校验，字符长度限制
- [ ] SMART原则提示集成
- [ ] 目标CRUD操作完整实现
- [ ] 数据持久化到本地数据库

#### FR-GI-002: AI辅助目标解析
**优先级**: P1 (V1.1版本)

**功能描述**:
通过自然语言处理辅助用户快速创建结构化目标

**技术规格**:
```typescript
interface NLPParseResult {
  extractedName?: string;
  extractedDescription?: string;
  extractedDeadline?: Date;
  extractedKeywords: string[];
  confidence: number; // 0-1
}

interface NLPService {
  parseGoalText(input: string): Promise<NLPParseResult>;
  extractKeywords(text: string): string[];
  identifyTimeReferences(text: string): Date[];
}
```

**技术选型**:
- 集成开源NLP库 (如 compromise.js)
- 本地处理，无需外部API调用

### 2.2 第一性原理目标分解模块 (First Principles Decomposition)

#### FR-FD-001: 引导式目标分解
**优先级**: P0 (MVP必需)

**功能描述**:
通过引导性问题帮助用户将目标分解为可执行的任务单元

**技术规格**:
```typescript
interface GoalNode {
  id: string;
  parentId?: string;
  type: 'goal' | 'subgoal' | 'milestone' | 'task';
  title: string;
  description?: string;
  children: GoalNode[];
  level: number; // 层级深度
  order: number; // 同级排序
}

interface DecompositionGuide {
  questionTemplates: string[];
  currentLevel: number;
  suggestedNextSteps: string[];
}
```

**验收标准**:
- [ ] 多层级树状结构支持
- [ ] 引导问题动态生成
- [ ] 节点拖拽排序功能
- [ ] 最小任务单元验证（2小时内可完成）

#### FR-FD-002: 结构可视化
**优先级**: P0 (MVP必需)

**功能描述**:
树状图和网状图可视化目标分解结构

**技术规格**:
- 使用D3.js或类似库实现交互式树状图
- 支持节点拖拽、缩放、折叠/展开
- 任务依赖关系可视化 (V1.2+)

**性能要求**:
- 支持至少1000个节点的流畅渲染
- 交互响应时间 < 100ms

### 2.3 任务管理模块 (Task Management)

#### FR-TM-001: 任务属性管理
**优先级**: P0 (MVP必需)

**技术规格**:
```typescript
interface Task {
  id: string;
  goalNodeId: string;
  title: string;
  description?: string;
  estimatedTime?: number; // 分钟
  actualTime: number; // 分钟，自动累计
  priority: 'high' | 'medium' | 'low';
  deadline?: Date;
  tags: string[];
  status: 'todo' | 'in-progress' | 'completed' | 'paused' | 'cancelled';
  completionPercentage: number; // 0-100
  recurrenceRule?: RecurrenceRule;
  createdAt: Date;
  updatedAt: Date;
}

interface RecurrenceRule {
  type: 'daily' | 'weekly' | 'monthly' | 'custom';
  interval: number;
  daysOfWeek?: number[]; // 0-6, 周日为0
  dayOfMonth?: number; // 1-31
  endDate?: Date;
}
```

#### FR-TM-002: 任务视图与筛选
**优先级**: P0 (MVP必需)

**技术规格**:
```typescript
interface TaskView {
  type: 'list' | 'kanban' | 'calendar' | 'hierarchy';
  filters: TaskFilter[];
  sorting: TaskSorting;
  grouping?: TaskGrouping;
}

interface TaskFilter {
  field: keyof Task;
  operator: 'equals' | 'contains' | 'greater-than' | 'less-than' | 'between';
  value: any;
}
```

**验收标准**:
- [ ] 四种视图模式正常切换
- [ ] 多条件筛选和排序
- [ ] 智能分组（今日待办、本周截止等）
- [ ] 视图状态持久化

### 2.4 定时提醒系统 (Intelligent Reminder)

#### FR-IR-001: 任务时间提醒
**优先级**: P0 (MVP必需)

**技术规格**:
```typescript
interface Reminder {
  id: string;
  taskId: string;
  type: 'start' | 'deadline' | 'custom';
  triggerTime: Date;
  message: string;
  isActive: boolean;
  notificationMethod: 'desktop' | 'in-app' | 'both';
}

interface NotificationService {
  scheduleReminder(reminder: Reminder): void;
  cancelReminder(reminderId: string): void;
  sendNotification(message: string, type: 'info' | 'warning' | 'error'): void;
}
```

**技术实现**:
- 使用系统原生通知API
- 后台定时器管理提醒队列
- 提醒持久化，应用重启后恢复

#### FR-IR-002: 间隔提醒 (Focus Pacer)
**优先级**: P1 (V1.1版本)

**功能描述**:
在长时间专注工作中定期进行进度检查提醒

**技术规格**:
```typescript
interface FocusPacer {
  interval: number; // 分钟
  isEnabled: boolean;
  messageTemplates: string[];
  lastTriggerTime?: Date;
}
```

### 2.5 番茄工作法模块 (Pomodoro Flow)

#### FR-PF-001: 自定义番茄钟
**优先级**: P0 (MVP必需)

**技术规格**:
```typescript
interface PomodoroSettings {
  workDuration: number; // 分钟，默认25
  shortBreakDuration: number; // 分钟，默认5
  longBreakDuration: number; // 分钟，默认15
  sessionsUntilLongBreak: number; // 默认4
  autoStartBreaks: boolean;
  autoStartNextSession: boolean;
}

interface PomodoroSession {
  id: string;
  taskId: string;
  type: 'work' | 'short-break' | 'long-break';
  startTime: Date;
  endTime?: Date;
  duration: number; // 分钟
  isCompleted: boolean;
  wasInterrupted: boolean;
}

interface PomodoroTimer {
  currentSession?: PomodoroSession;
  remainingTime: number; // 秒
  isRunning: boolean;
  isPaused: boolean;
  sessionCount: number; // 当日完成的番茄数
}
```

**验收标准**:
- [ ] 计时器精确到秒级
- [ ] 状态持久化，支持应用重启后恢复
- [ ] 音频和视觉提醒集成
- [ ] 与任务系统联动，自动记录工作时长

### 2.6 专注力监测模块 (Focus Enhancement & Monitoring)

#### FR-FM-001: 环境监测 (Digital Sentinel)
**优先级**: P1 (V1.1版本)

**技术规格**:
```typescript
interface ApplicationMonitor {
  whitelistApps: string[]; // 应用路径或包名
  blacklistApps: string[];
  whitelistWebsites: string[]; // 域名
  blacklistWebsites: string[];
  isMonitoring: boolean;
  
  startMonitoring(): void;
  stopMonitoring(): void;
  getCurrentActiveApp(): Promise<string>;
  blockApplication(appPath: string): Promise<boolean>;
}

interface ActivityLog {
  timestamp: Date;
  application: string;
  website?: string;
  duration: number; // 秒
  isDistraction: boolean;
}
```

**技术实现**:
- **Windows**: 使用Windows API获取活动窗口信息
- **macOS**: 使用Accessibility API和App Usage API
- **权限处理**: 明确提示用户并获取必要的系统权限

**安全考虑**:
- 仅记录应用名称和域名，不记录具体内容
- 提供完全本地化的数据存储选项
- 用户可随时禁用监测功能

#### FR-FM-002: 定时专注检查
**优先级**: P1 (V1.1版本)

**技术规格**:
```typescript
interface FocusCheckIn {
  id: string;
  timestamp: Date;
  taskId: string;
  response: 'focused' | 'distracted' | 'task-switch';
  reason?: string;
  emotionalState?: 'calm' | 'stressed' | 'energetic' | 'tired';
}

interface FocusChecker {
  interval: number; // 分钟
  isEnabled: boolean;
  showCheckIn(): Promise<FocusCheckIn>;
}
```

#### FR-FM-003: 深潜模式 (Deep Focus Mode)
**优先级**: P1 (V1.1版本)

**技术规格**:
```typescript
interface DeepFocusMode {
  isActive: boolean;
  taskId: string;
  startTime: Date;
  targetDuration: number; // 分钟
  exitRequiresConfirmation: boolean;
  confirmationDelay: number; // 秒，冷静期
  blockedApplications: string[];
  blockedWebsites: string[];
}

interface DeepFocusControls {
  activate(taskId: string, duration: number): Promise<void>;
  deactivate(reason?: string): Promise<void>;
  requestExit(): Promise<boolean>; // 返回是否允许退出
}
```

### 2.7 数据分析模块 (Analytics & Insights)

#### FR-DA-001: 专注度统计
**优先级**: P1 (V1.1版本)

**技术规格**:
```typescript
interface FocusMetrics {
  date: Date;
  totalFocusTime: number; // 分钟
  pomodoroSessions: number;
  deepFocusSessions: number;
  distractionCount: number;
  taskCompletionRate: number; // 0-1
  averageTaskDuration: number; // 分钟
}

interface ProductivityReport {
  period: 'day' | 'week' | 'month';
  startDate: Date;
  endDate: Date;
  metrics: FocusMetrics[];
  insights: string[];
  recommendations: string[];
}
```

#### FR-DA-002: 个性化建议引擎
**优先级**: P2 (V1.2版本)

**技术规格**:
```typescript
interface UserBehaviorPattern {
  mostProductiveHours: number[];
  averageFocusSessionLength: number;
  commonDistractionSources: string[];
  taskEstimationAccuracy: number;
  preferredBreakDuration: number;
}

interface RecommendationEngine {
  analyzeUserBehavior(userId: string): UserBehaviorPattern;
  generateRecommendations(pattern: UserBehaviorPattern): Recommendation[];
}

interface Recommendation {
  type: 'pomodoro-settings' | 'schedule-optimization' | 'distraction-management';
  title: string;
  description: string;
  actionable: boolean;
  priority: 'high' | 'medium' | 'low';
}
```

## 3. 非功能性需求

### 3.1 性能需求
- **启动时间**: 冷启动 ≤ 5秒，热启动 ≤ 2秒
- **响应时间**: UI交互 ≤ 200ms，数据查询 ≤ 1秒
- **资源占用**: 
  - 空闲时CPU ≤ 5%
  - 内存占用 ≤ 200MB
  - 磁盘空间 ≤ 500MB

### 3.2 兼容性需求
- **操作系统**: 
  - Windows 10+ (x64)
  - macOS 10.15+ (Intel & Apple Silicon)
- **浏览器引擎**: Chromium 90+（Electron内置）

### 3.3 安全需求
- 所有敏感数据本地AES-256加密存储
- 系统监测功能明确权限申请和数据用途说明
- 不收集用户个人身份信息
- 支持数据导出和完全删除

### 3.4 可用性需求
- 界面响应式设计, 支持不同屏幕尺寸
- 完整的键盘导航支持
- 错误信息友好且可操作
- 支持撤销/重做操作
- 提供离线帮助文档

### 3.5 数据完整性需求
- 自动数据备份机制
- 异常情况下的数据恢复
- 数据迁移和版本兼容性
- 事务性操作确保数据一致性

## 4. 技术约束

### 4.1 开发约束
- 使用TypeScript进行类型安全开发
- 遵循ESLint和Prettier代码规范
- 单元测试覆盖率 ≥ 80%
- 使用语义化版本管理

### 4.2 部署约束
- 支持独立安装包分发
- 不依赖外部服务器（核心功能）
- 支持静默更新机制
- 多语言支持准备（i18n）

## 5. 验收标准

### 5.1 功能验收
- [ ] 所有P0功能完整实现并通过测试
- [ ] 核心用户场景端到端测试通过
- [ ] 性能指标达到要求
- [ ] 安全扫描无高危漏洞

### 5.2 用户验收
- [ ] 用户可在15分钟内完成首次目标创建和任务分解
- [ ] 番茄钟功能正常工作，计时准确
- [ ] 应用崩溃率 < 0.1%
- [ ] 用户反馈满意度 ≥ 4.0/5.0

## 6. 里程碑规划

### MVP (3-4个月)
- [ ] 基础目标和任务管理
- [ ] 番茄钟功能
- [ ] 简单提醒系统
- [ ] 基础数据可视化

### V1.1 (MVP后2-3个月)
- [ ] 环境监测功能
- [ ] 深潜模式
- [ ] 专注度分析
- [ ] AI辅助目标解析

### V1.2 (V1.1后3-4个月)
- [ ] 个性化建议引擎
- [ ] 高级数据分析
- [ ] 社区功能探索
- [ ] 移动端支持准备

---

**文档维护**: 本文档将随着产品迭代持续更新，所有变更需经过技术评审确认。 