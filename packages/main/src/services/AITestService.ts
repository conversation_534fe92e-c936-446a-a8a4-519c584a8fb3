import { GoogleGenerativeAI } from '@google/generative-ai';
import axios from 'axios';

export interface AITestResult {
  success: boolean;
  message: string;
  responseTime?: number;
  error?: string;
}

export class AITestService {
  private static readonly TEST_MESSAGE = "hello";
  private static readonly TIMEOUT = 10000; // 10秒超时

  /**
   * 统一的错误处理方法
   */
  private static handleApiError(error: any): AITestResult {
    if (axios.isAxiosError(error)) {
      const status = error.response?.status;
      const statusText = error.response?.statusText;

      // 特殊处理常见错误
      let userFriendlyMessage = 'API连接测试失败';
      let userFriendlyError = `HTTP ${status} ${statusText}`;

      if (status === 429) {
        userFriendlyMessage = 'API配额已用完';
        userFriendlyError = '您已超出当前配额限制，请检查您的计划和账单详情，或稍后再试';
      } else if (status === 401) {
        userFriendlyMessage = 'API Key无效';
        userFriendlyError = '请检查您的API Key是否正确';
      } else if (status === 403) {
        userFriendlyMessage = '访问被拒绝';
        userFriendlyError = '请检查您的API Key权限';
      } else if (status === 404) {
        userFriendlyMessage = 'API地址不存在';
        userFriendlyError = '请检查您的Base URL和模型ID是否正确';
      } else if (status === 500) {
        userFriendlyMessage = '服务器内部错误';
        userFriendlyError = 'API服务器出现问题，请稍后重试';
      }

      return {
        success: false,
        message: userFriendlyMessage,
        error: userFriendlyError
      };
    }

    return {
      success: false,
      message: 'API连接测试失败',
      error: error instanceof Error ? error.message : String(error)
    };
  }

  /**
   * 测试AI提供商连接
   */
  static async testProvider(provider: {
    name: string;
    baseUrl: string;
    apiKey: string;
    modelId: string;
  }): Promise<AITestResult> {
    const startTime = Date.now();
    
    try {
      console.log(`开始测试 ${provider.name} 连接...`);
      
      // 根据提供商类型选择测试方法
      let result: AITestResult;

      if (provider.name.toLowerCase().includes('google') ||
          provider.name.toLowerCase().includes('gemini')) {
        result = await this.testGoogleAI(provider);
      } else if (provider.name.toLowerCase().includes('openrouter')) {
        result = await this.testOpenRouter(provider);
      } else if (provider.name.toLowerCase().includes('openai') ||
                 provider.name.toLowerCase().includes('gpt')) {
        result = await this.testOpenAI(provider);
      } else if (provider.name.toLowerCase().includes('anthropic') ||
                 provider.name.toLowerCase().includes('claude')) {
        result = await this.testAnthropic(provider);
      } else {
        // 通用OpenAI兼容API测试
        result = await this.testGenericOpenAI(provider);
      }
      
      result.responseTime = Date.now() - startTime;
      return result;
      
    } catch (error) {
      console.error(`测试 ${provider.name} 失败:`, error);
      return {
        success: false,
        message: 'API连接测试失败',
        error: error instanceof Error ? error.message : String(error),
        responseTime: Date.now() - startTime
      };
    }
  }

  /**
   * 测试Google AI (Gemini) - 使用REST API
   */
  private static async testGoogleAI(provider: {
    name: string;
    baseUrl: string;
    apiKey: string;
    modelId: string;
  }): Promise<AITestResult> {
    try {
      // 使用官方REST API格式
      const url = `https://generativelanguage.googleapis.com/v1beta/models/${provider.modelId}:generateContent?key=${provider.apiKey}`;

      const requestBody = {
        contents: [
          {
            parts: [
              {
                text: this.TEST_MESSAGE
              }
            ]
          }
        ]
      };

      const response = await axios.post(url, requestBody, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: this.TIMEOUT
      });

      const text = response.data?.candidates?.[0]?.content?.parts?.[0]?.text;

      if (text && text.trim().length > 0) {
        console.log(`Google AI 响应: ${text.substring(0, 100)}...`);
        return {
          success: true,
          message: 'API连接测试成功'
        };
      } else {
        return {
          success: false,
          message: 'API连接测试失败',
          error: '收到空响应'
        };
      }
    } catch (error) {
      return this.handleApiError(error);
    }
  }

  /**
   * 测试OpenRouter API
   */
  private static async testOpenRouter(provider: {
    name: string;
    baseUrl: string;
    apiKey: string;
    modelId: string;
  }): Promise<AITestResult> {
    try {
      const response = await axios.post(
        `${provider.baseUrl}/chat/completions`,
        {
          model: provider.modelId,
          messages: [
            {
              role: 'user',
              content: this.TEST_MESSAGE
            }
          ],
          max_tokens: 50
        },
        {
          headers: {
            'Authorization': `Bearer ${provider.apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://focusos.app', // OpenRouter推荐的站点标识
            'X-Title': 'FocusOS' // OpenRouter推荐的应用标识
          },
          timeout: this.TIMEOUT
        }
      );

      const text = response.data?.choices?.[0]?.message?.content;

      if (text && text.trim().length > 0) {
        console.log(`OpenRouter 响应: ${text.substring(0, 100)}...`);
        return {
          success: true,
          message: 'API连接测试成功'
        };
      } else {
        return {
          success: false,
          message: 'API连接测试失败',
          error: '收到空响应'
        };
      }
    } catch (error) {
      return this.handleApiError(error);
    }
  }

  /**
   * 测试OpenAI API
   */
  private static async testOpenAI(provider: {
    name: string;
    baseUrl: string;
    apiKey: string;
    modelId: string;
  }): Promise<AITestResult> {
    try {
      const response = await axios.post(
        `${provider.baseUrl}/chat/completions`,
        {
          model: provider.modelId,
          messages: [
            {
              role: 'user',
              content: this.TEST_MESSAGE
            }
          ],
          max_tokens: 50
        },
        {
          headers: {
            'Authorization': `Bearer ${provider.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: this.TIMEOUT
        }
      );

      const text = response.data?.choices?.[0]?.message?.content;
      
      if (text && text.trim().length > 0) {
        console.log(`OpenAI 响应: ${text.substring(0, 100)}...`);
        return {
          success: true,
          message: 'API连接测试成功'
        };
      } else {
        return {
          success: false,
          message: 'API连接测试失败',
          error: '收到空响应'
        };
      }
    } catch (error) {
      return this.handleApiError(error);
    }
  }

  /**
   * 测试Anthropic API
   */
  private static async testAnthropic(provider: {
    name: string;
    baseUrl: string;
    apiKey: string;
    modelId: string;
  }): Promise<AITestResult> {
    try {
      const response = await axios.post(
        `${provider.baseUrl}/messages`,
        {
          model: provider.modelId,
          max_tokens: 50,
          messages: [
            {
              role: 'user',
              content: this.TEST_MESSAGE
            }
          ]
        },
        {
          headers: {
            'x-api-key': provider.apiKey,
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01'
          },
          timeout: this.TIMEOUT
        }
      );

      const text = response.data?.content?.[0]?.text;
      
      if (text && text.trim().length > 0) {
        console.log(`Anthropic 响应: ${text.substring(0, 100)}...`);
        return {
          success: true,
          message: 'API连接测试成功'
        };
      } else {
        return {
          success: false,
          message: 'API连接测试失败',
          error: '收到空响应'
        };
      }
    } catch (error) {
      return this.handleApiError(error);
    }
  }

  /**
   * 测试通用OpenAI兼容API
   */
  private static async testGenericOpenAI(provider: {
    name: string;
    baseUrl: string;
    apiKey: string;
    modelId: string;
  }): Promise<AITestResult> {
    try {
      // 尝试OpenAI格式
      const response = await axios.post(
        `${provider.baseUrl}/chat/completions`,
        {
          model: provider.modelId,
          messages: [
            {
              role: 'user',
              content: this.TEST_MESSAGE
            }
          ],
          max_tokens: 50
        },
        {
          headers: {
            'Authorization': `Bearer ${provider.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: this.TIMEOUT
        }
      );

      const text = response.data?.choices?.[0]?.message?.content;
      
      if (text && text.trim().length > 0) {
        console.log(`${provider.name} 响应: ${text.substring(0, 100)}...`);
        return {
          success: true,
          message: 'API连接测试成功'
        };
      } else {
        return {
          success: false,
          message: 'API连接测试失败',
          error: '收到空响应'
        };
      }
    } catch (error) {
      return this.handleApiError(error);
    }
  }
}
