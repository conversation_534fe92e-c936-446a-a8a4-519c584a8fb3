import { GoogleGenerativeAI } from '@google/generative-ai';
import axios from 'axios';

export interface AITestResult {
  success: boolean;
  message: string;
  responseTime?: number;
  error?: string;
}

export class AITestService {
  private static readonly TEST_MESSAGE = "hello";
  private static readonly TIMEOUT = 10000; // 10秒超时

  /**
   * 统一的错误处理方法
   */
  private static handleApiError(error: any): AITestResult {
    if (axios.isAxiosError(error)) {
      const status = error.response?.status;
      const statusText = error.response?.statusText;

      // 特殊处理常见错误
      let userFriendlyMessage = 'API连接测试失败';
      let userFriendlyError = `HTTP ${status} ${statusText}`;

      if (status === 429) {
        userFriendlyMessage = 'API配额已用完';
        userFriendlyError = '您已超出当前配额限制，请检查您的计划和账单详情，或稍后再试';
      } else if (status === 401) {
        userFriendlyMessage = 'API Key无效';
        userFriendlyError = '请检查您的API Key是否正确';
      } else if (status === 403) {
        userFriendlyMessage = '访问被拒绝';
        userFriendlyError = '请检查您的API Key权限';
      } else if (status === 404) {
        userFriendlyMessage = 'API地址不存在';
        userFriendlyError = '请检查您的Base URL和模型ID是否正确';
      } else if (status === 500) {
        userFriendlyMessage = '服务器内部错误';
        userFriendlyError = 'API服务器出现问题，请稍后重试';
      }

      return {
        success: false,
        message: userFriendlyMessage,
        error: userFriendlyError
      };
    }

    return {
      success: false,
      message: 'API连接测试失败',
      error: error instanceof Error ? error.message : String(error)
    };
  }

  /**
   * 测试AI提供商连接
   */
  static async testProvider(provider: {
    name: string;
    baseUrl: string;
    apiKey: string;
    modelId: string;
  }): Promise<AITestResult> {
    const startTime = Date.now();
    
    try {
      console.log(`开始测试 ${provider.name} 连接...`);
      
      // 根据提供商类型选择测试方法
      let result: AITestResult;

      if (provider.name.toLowerCase().includes('google') ||
          provider.name.toLowerCase().includes('gemini')) {
        result = await this.testGoogleAI(provider);
      } else if (provider.name.toLowerCase().includes('openrouter')) {
        result = await this.testOpenRouter(provider);
      } else if (provider.name.toLowerCase().includes('openai') ||
                 provider.name.toLowerCase().includes('gpt')) {
        result = await this.testOpenAI(provider);
      } else if (provider.name.toLowerCase().includes('anthropic') ||
                 provider.name.toLowerCase().includes('claude')) {
        result = await this.testAnthropic(provider);
      } else {
        // 通用OpenAI兼容API测试
        result = await this.testGenericOpenAI(provider);
      }
      
      result.responseTime = Date.now() - startTime;
      return result;
      
    } catch (error) {
      console.error(`测试 ${provider.name} 失败:`, error);
      return {
        success: false,
        message: 'API连接测试失败',
        error: error instanceof Error ? error.message : String(error),
        responseTime: Date.now() - startTime
      };
    }
  }

  /**
   * 测试Google AI (Gemini) - 使用REST API
   */
  private static async testGoogleAI(provider: {
    name: string;
    baseUrl: string;
    apiKey: string;
    modelId: string;
  }): Promise<AITestResult> {
    try {
      // 使用官方REST API格式
      const url = `https://generativelanguage.googleapis.com/v1beta/models/${provider.modelId}:generateContent?key=${provider.apiKey}`;

      const requestBody = {
        contents: [
          {
            parts: [
              {
                text: this.TEST_MESSAGE
              }
            ]
          }
        ]
      };

      const response = await axios.post(url, requestBody, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: this.TIMEOUT
      });

      const text = response.data?.candidates?.[0]?.content?.parts?.[0]?.text;

      if (text && text.trim().length > 0) {
        console.log(`Google AI 响应: ${text.substring(0, 100)}...`);
        return {
          success: true,
          message: 'API连接测试成功'
        };
      } else {
        return {
          success: false,
          message: 'API连接测试失败',
          error: '收到空响应'
        };
      }
    } catch (error) {
      return this.handleApiError(error);
    }
  }

  /**
   * 测试OpenRouter API
   */
  private static async testOpenRouter(provider: {
    name: string;
    baseUrl: string;
    apiKey: string;
    modelId: string;
  }): Promise<AITestResult> {
    try {
      // 确保baseUrl格式正确，移除末尾斜杠并添加正确的端点
      const baseUrl = provider.baseUrl.replace(/\/+$/, ''); // 移除末尾的所有斜杠
      const fullUrl = `${baseUrl}/chat/completions`;
      
      console.log(`[OpenRouter] 测试开始:`);
      console.log(`[OpenRouter] 原始Base URL: ${provider.baseUrl}`);
      console.log(`[OpenRouter] 处理后Base URL: ${baseUrl}`);
      console.log(`[OpenRouter] 完整URL: ${fullUrl}`);
      console.log(`[OpenRouter] 模型ID: ${provider.modelId}`);
      console.log(`[OpenRouter] API Key前缀: ${provider.apiKey.substring(0, 10)}...`);

      const requestBody = {
        model: provider.modelId,
        messages: [
          {
            role: 'user',
            content: this.TEST_MESSAGE
          }
        ],
        max_tokens: 50
      };

      const requestHeaders = {
        'Authorization': `Bearer ${provider.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://focusos.app', // OpenRouter推荐的站点标识
        'X-Title': 'FocusOS' // OpenRouter推荐的应用标识
      };

      console.log(`[OpenRouter] 请求体:`, JSON.stringify(requestBody, null, 2));
      console.log(`[OpenRouter] 请求头:`, JSON.stringify({
        ...requestHeaders,
        'Authorization': `Bearer ${provider.apiKey.substring(0, 10)}...`
      }, null, 2));

      const response = await axios.post(fullUrl, requestBody, {
        headers: requestHeaders,
        timeout: this.TIMEOUT
      });

      console.log(`[OpenRouter] 响应状态: ${response.status}`);
      console.log(`[OpenRouter] 响应头:`, JSON.stringify(response.headers, null, 2));
      console.log(`[OpenRouter] 响应数据:`, JSON.stringify(response.data, null, 2));

      const messageData = response.data?.choices?.[0]?.message;
      const content = messageData?.content;
      const reasoning = messageData?.reasoning;

      // 对于推理模型（如DeepSeek R1），内容可能在reasoning字段中
      let responseText = '';
      if (content && content.trim().length > 0) {
        responseText = content;
      } else if (reasoning && reasoning.trim().length > 0) {
        responseText = reasoning;
        console.log(`[OpenRouter] 检测到推理模型响应，使用reasoning字段`);
      }

      if (responseText && responseText.trim().length > 0) {
        console.log(`[OpenRouter] 成功响应: ${responseText.substring(0, 100)}...`);
        return {
          success: true,
          message: 'API连接测试成功'
        };
      } else {
        console.log(`[OpenRouter] 空响应错误 - content: "${content}", reasoning: "${reasoning}"`);
        return {
          success: false,
          message: 'API连接测试失败',
          error: '收到空响应'
        };
      }
    } catch (error) {
      console.log(`[OpenRouter] 请求失败:`, error);
      if (axios.isAxiosError(error)) {
        console.log(`[OpenRouter] Axios错误详情:`);
        console.log(`[OpenRouter] - 状态码: ${error.response?.status}`);
        console.log(`[OpenRouter] - 状态文本: ${error.response?.statusText}`);
        console.log(`[OpenRouter] - 错误数据:`, JSON.stringify(error.response?.data, null, 2));
        console.log(`[OpenRouter] - 请求配置:`, JSON.stringify({
          url: error.config?.url,
          method: error.config?.method,
          headers: { ...error.config?.headers, 'Authorization': '[HIDDEN]' }
        }, null, 2));
      }
      return this.handleApiError(error);
    }
  }

  /**
   * 测试OpenAI API
   */
  private static async testOpenAI(provider: {
    name: string;
    baseUrl: string;
    apiKey: string;
    modelId: string;
  }): Promise<AITestResult> {
    try {
      const response = await axios.post(
        `${provider.baseUrl}/chat/completions`,
        {
          model: provider.modelId,
          messages: [
            {
              role: 'user',
              content: this.TEST_MESSAGE
            }
          ],
          max_tokens: 50
        },
        {
          headers: {
            'Authorization': `Bearer ${provider.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: this.TIMEOUT
        }
      );

      const messageData = response.data?.choices?.[0]?.message;
      const content = messageData?.content;
      const reasoning = messageData?.reasoning;

      // 对于推理模型，内容可能在reasoning字段中
      let responseText = '';
      if (content && content.trim().length > 0) {
        responseText = content;
      } else if (reasoning && reasoning.trim().length > 0) {
        responseText = reasoning;
      }
      
      if (responseText && responseText.trim().length > 0) {
        console.log(`OpenAI 响应: ${responseText.substring(0, 100)}...`);
        return {
          success: true,
          message: 'API连接测试成功'
        };
      } else {
        return {
          success: false,
          message: 'API连接测试失败',
          error: '收到空响应'
        };
      }
    } catch (error) {
      return this.handleApiError(error);
    }
  }

  /**
   * 测试Anthropic API
   */
  private static async testAnthropic(provider: {
    name: string;
    baseUrl: string;
    apiKey: string;
    modelId: string;
  }): Promise<AITestResult> {
    try {
      const response = await axios.post(
        `${provider.baseUrl}/messages`,
        {
          model: provider.modelId,
          max_tokens: 50,
          messages: [
            {
              role: 'user',
              content: this.TEST_MESSAGE
            }
          ]
        },
        {
          headers: {
            'x-api-key': provider.apiKey,
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01'
          },
          timeout: this.TIMEOUT
        }
      );

      const text = response.data?.content?.[0]?.text;
      
      if (text && text.trim().length > 0) {
        console.log(`Anthropic 响应: ${text.substring(0, 100)}...`);
        return {
          success: true,
          message: 'API连接测试成功'
        };
      } else {
        return {
          success: false,
          message: 'API连接测试失败',
          error: '收到空响应'
        };
      }
    } catch (error) {
      return this.handleApiError(error);
    }
  }

  /**
   * 测试通用OpenAI兼容API
   */
  private static async testGenericOpenAI(provider: {
    name: string;
    baseUrl: string;
    apiKey: string;
    modelId: string;
  }): Promise<AITestResult> {
    try {
      // 尝试OpenAI格式
      const response = await axios.post(
        `${provider.baseUrl}/chat/completions`,
        {
          model: provider.modelId,
          messages: [
            {
              role: 'user',
              content: this.TEST_MESSAGE
            }
          ],
          max_tokens: 50
        },
        {
          headers: {
            'Authorization': `Bearer ${provider.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: this.TIMEOUT
        }
      );

      const messageData = response.data?.choices?.[0]?.message;
      const content = messageData?.content;
      const reasoning = messageData?.reasoning;

      // 对于推理模型，内容可能在reasoning字段中
      let responseText = '';
      if (content && content.trim().length > 0) {
        responseText = content;
      } else if (reasoning && reasoning.trim().length > 0) {
        responseText = reasoning;
      }
      
      if (responseText && responseText.trim().length > 0) {
        console.log(`${provider.name} 响应: ${responseText.substring(0, 100)}...`);
        return {
          success: true,
          message: 'API连接测试成功'
        };
      } else {
        return {
          success: false,
          message: 'API连接测试失败',
          error: '收到空响应'
        };
      }
    } catch (error) {
      return this.handleApiError(error);
    }
  }
}
