import React, { useCallback, useMemo, useEffect, useState } from 'react';
import { Modal, Form, Input, Select, DatePicker, InputNumber, Row, Col, Checkbox, Tooltip, Space, Button } from 'antd';
import { InfoCircleOutlined, RobotOutlined, ReloadOutlined } from '@ant-design/icons';
import { Goal } from '../types';
import SmartGoalHelper from './SmartGoalHelper';
import FormProgressIndicator from './FormProgressIndicator';
import AIDecompositionConfig, { AIDecompositionConfig as AIConfig } from './AIDecompositionConfig';
import dayjs from 'dayjs';

const { TextArea } = Input;
const { Option } = Select;

interface GoalFormModalProps {
  visible: boolean;
  editingGoal: Goal | null;
  goalType: string;
  repeatType: string;
  customRepeat: { interval: number; unit: string; weekdays?: number[] };
  formValues: Record<string, any>;
  form: any; // 新增: 接收外部传入的form实例
  onCancel: () => void;
  onSubmit: (values: any) => void;
  onGoalTypeChange: (value: string) => void;
  onRepeatTypeChange: (value: string) => void;
  onCustomRepeatChange: (value: any) => void;
  onFormValuesChange: (changedValues: any, allValues: any) => void;
  // AI分解相关
  aiDecompositionEnabled?: boolean;
  aiDecompositionConfig?: AIConfig;
  onAIDecompositionEnabledChange?: (enabled: boolean) => void;
  onAIDecompositionConfigChange?: (config: AIConfig) => void;
  // 新增: AI分解按钮处理
  onAIDecomposition?: (goal: Goal) => void;
  onRedecomposition?: (goal: Goal) => void;
}

const GoalFormModal: React.FC<GoalFormModalProps> = React.memo(({
  visible,
  editingGoal,
  goalType,
  repeatType,
  customRepeat,
  formValues,
  form, // 接收外部传入的form实例
  onCancel,
  onSubmit,
  onGoalTypeChange,
  onRepeatTypeChange,
  onCustomRepeatChange,
  onFormValuesChange,
  aiDecompositionEnabled = false,
  aiDecompositionConfig,
  onAIDecompositionEnabledChange,
  onAIDecompositionConfigChange,
  onAIDecomposition,
  onRedecomposition
}) => {

  // AI分解配置的默认值
  const defaultAIConfig: AIConfig = useMemo(() => ({
    preferences: {
      maxDepth: 3,
      taskGranularity: 'medium',
      includeTimeEstimates: true,
      maxTaskDuration: 120,
      focusAreas: []
    },
    context: {
      userExperience: 'intermediate',
      availableTime: '每天1-2小时',
      resources: [],
      constraints: []
    }
  }), []);

  const currentAIConfig = aiDecompositionConfig || defaultAIConfig;

  // 缓存必填字段
  const requiredFields = useMemo(() => {
    return goalType === 'long-term' 
      ? ['name', 'description', 'type', 'whyPower', 'repeatType'] 
      : ['name', 'description', 'type', 'whyPower'];
  }, [goalType]);

  const optionalFields = useMemo(() => ['domains', 'startDate', 'deadline'], []);

  // 处理自定义重复设置的回调
  const handleCustomRepeatChange = useCallback((field: string, value: any) => {
    onCustomRepeatChange({ ...customRepeat, [field]: value });
  }, [customRepeat, onCustomRepeatChange]);

  // 当编辑目标时，设置表单初始值
  useEffect(() => {
    if (visible && editingGoal) {
      const formData = {
        ...editingGoal,
        startDate: editingGoal.startDate ? dayjs(editingGoal.startDate) : null,
        deadline: editingGoal.deadline ? dayjs(editingGoal.deadline) : null,
      };
      form.setFieldsValue(formData);
    } else if (visible && !editingGoal) {
      form.resetFields();
    }
  }, [visible, editingGoal, form]);

  return (
    <Modal
      title={editingGoal ? '编辑目标' : '新建目标'}
      open={visible}
      onCancel={onCancel}
      onOk={() => form.submit()}
      width={600}
      destroyOnClose={true}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        editingGoal && !editingGoal.hasAIDecomposition && onAIDecomposition && (
          <Button
            key="ai-decomposition"
            icon={<RobotOutlined />}
            onClick={() => onAIDecomposition(editingGoal)}
            style={{ marginRight: 'auto' }}
            type="dashed"
          >
            AI分解任务
          </Button>
        ),
        editingGoal && editingGoal.hasAIDecomposition && onRedecomposition && (
          <Button
            key="redecomposition"
            icon={<ReloadOutlined />}
            onClick={() => onRedecomposition(editingGoal)}
            style={{ marginRight: 'auto' }}
            type="dashed"
          >
            重新AI分解
          </Button>
        ),
        <Button key="submit" type="primary" onClick={() => form.submit()}>
          {editingGoal ? '更新' : '创建'}
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={onSubmit}
        onValuesChange={onFormValuesChange}
      >
        {/* 表单进度指示器 */}
        <FormProgressIndicator
          formValues={formValues}
          requiredFields={requiredFields}
          optionalFields={optionalFields}
          compact={true}
        />

        <Form.Item
          name="name"
          label="目标名称"
          rules={[
            { required: true, message: '请输入目标名称' },
            { min: 2, message: '目标名称至少需要2个字符' },
            { max: 50, message: '目标名称不能超过50个字符' }
          ]}
        >
          <Input
            className="fast-input"
            placeholder="简洁明确地描述你的目标（2-50字符）"
            maxLength={50}
            showCount
          />
        </Form.Item>

        <Form.Item
          name="description"
          label="目标描述"
          rules={[
            { required: true, message: '请输入目标描述' },
            { min: 10, message: '目标描述至少需要10个字符' },
            { max: 1000, message: '目标描述不能超过1000个字符' },
            {
              validator: (_, value) => {
                if (!value) return Promise.resolve();

                // SMART原则基础检查
                const hasNumbers = /\d/.test(value);
                const hasTimeWords = /(天|周|月|年|小时|分钟|日期|时间|期限|截止|完成|达到)/.test(value);
                const hasActionWords = /(学习|完成|提高|减少|增加|实现|掌握|获得|建立|改善)/.test(value);

                if (!hasActionWords) {
                  return Promise.reject(new Error('建议在描述中包含明确的行动词汇（如：学习、完成、提高等）'));
                }

                if (!hasNumbers && !hasTimeWords) {
                  return Promise.reject(new Error('建议在描述中包含具体的数量或时间信息，使目标更可衡量'));
                }

                return Promise.resolve();
              }
            }
          ]}
        >
          <TextArea
            className="fast-input"
            rows={4}
            placeholder="详细描述你的目标，建议遵循SMART原则（具体、可衡量、可实现、相关、有时限）&#10;例如：在3个月内通过学习React和TypeScript，完成一个个人项目网站，提升前端开发技能"
            showCount
            maxLength={1000}
          />
        </Form.Item>

        {/* SMART原则提示 */}
        <SmartGoalHelper
          compact={true}
          goalDescription={formValues.description}
        />

        {/* AI智能分解配置 */}
        {!editingGoal && (
          <AIDecompositionConfig
            enabled={aiDecompositionEnabled}
            onEnabledChange={onAIDecompositionEnabledChange || (() => {})}
            config={currentAIConfig}
            onConfigChange={onAIDecompositionConfigChange || (() => {})}
            goalType={goalType}
            goalDescription={formValues.description}
          />
        )}

        <Form.Item
          name="type"
          label="目标类型"
          rules={[{ required: true, message: '请选择目标类型' }]}
          extra={
            <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
              {goalType === 'long-term' && '💡 长期目标：通常需要6个月以上时间，需要分解为多个阶段'}
              {goalType === 'short-term' && '💡 短期目标：通常在6个月内完成，有明确的截止日期'}
              {goalType === 'habit' && '💡 习惯养成：重复性行为，通过持续练习形成自动化'}
            </div>
          }
        >
          <Select
            className="fast-input"
            placeholder="选择目标类型"
            onChange={onGoalTypeChange}
          >
            <Option value="long-term">
              <div>
                <div style={{ fontWeight: 500 }}>长期目标</div>
                <div style={{ fontSize: '12px', color: '#999' }}>6个月以上，需要分阶段实现</div>
              </div>
            </Option>
            <Option value="short-term">
              <div>
                <div style={{ fontWeight: 500 }}>短期目标</div>
                <div style={{ fontSize: '12px', color: '#999' }}>6个月内完成，有明确期限</div>
              </div>
            </Option>
            <Option value="habit">
              <div>
                <div style={{ fontWeight: 500 }}>习惯养成</div>
                <div style={{ fontSize: '12px', color: '#999' }}>重复性行为，形成自动化</div>
              </div>
            </Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="whyPower"
          label={
            <Space>
              <span>核心驱动力 (Why Power)</span>
              <Tooltip title="描述这个目标对你的重要性和意义，这将成为你坚持下去的内在动力。强烈的内在动机是实现目标的关键因素。">
                <InfoCircleOutlined style={{ color: '#999' }} />
              </Tooltip>
            </Space>
          }
          rules={[
            { required: true, message: '请输入核心驱动力' },
            { min: 50, message: '核心驱动力至少需要50个字符，请更详细地描述你的动机' },
            { max: 500, message: '核心驱动力不能超过500个字符' },
            {
              validator: (_, value) => {
                if (!value) return Promise.resolve();

                // 检查是否包含情感词汇和深层动机
                const hasEmotionWords = /(希望|想要|渴望|梦想|害怕|担心|兴奋|激动|重要|意义|价值|成就|满足|快乐|幸福|自豪)/.test(value);
                const hasReasonWords = /(因为|由于|为了|目的|原因|动机|驱动|激励)/.test(value);
                const hasImpactWords = /(影响|改变|提升|改善|帮助|贡献|实现|获得|达到)/.test(value);

                if (!hasEmotionWords) {
                  return Promise.reject(new Error('建议描述你的情感动机，如"希望"、"渴望"、"担心"等'));
                }

                if (!hasReasonWords) {
                  return Promise.reject(new Error('建议说明具体原因，如"因为"、"为了"等'));
                }

                // if (!hasImpactWords) {
                //   return Promise.reject(new Error('建议描述预期的积极影响或改变'));
                // }

                return Promise.resolve();
              }
            }
          ]}
          extra={
            <div style={{ fontSize: '12px', color: '#666', lineHeight: '1.4' }}>
              <div style={{ marginBottom: 4 }}>💡 <strong>引导思考：</strong></div>
              <div>• 这个目标为什么对你重要？</div>
              <div>• 实现它会给你带来什么积极改变？</div>
              <div>• 不实现会有什么后果或遗憾？</div>
              <div>• 它如何与你的人生价值观相关？</div>
            </div>
          }
        >
          <TextArea
            className="fast-input"
            rows={5}
            placeholder="例如：我想学习编程是因为我希望转行到科技行业，获得更好的职业发展机会和薪资待遇。我渴望能够创造有价值的产品帮助他人，同时获得更大的工作自由度。如果不学习编程，我担心会一直停留在现在的工作中，无法实现我的职业理想和财务目标..."
            showCount
            maxLength={500}
          />
        </Form.Item>

        <Form.Item
          name="domains"
          label="关联领域"
        >
          <Select className="fast-input" mode="tags" placeholder="输入相关领域标签">
            <Option value="工作">工作</Option>
            <Option value="学习">学习</Option>
            <Option value="健康">健康</Option>
            <Option value="家庭">家庭</Option>
            <Option value="兴趣">兴趣</Option>
          </Select>
        </Form.Item>

        {/* 根据目标类型显示不同的日期/重复字段 */}
        {goalType === 'long-term' ? (
          // 长期目标显示重复周期
          <>
            <Form.Item
              name="repeatType"
              label="重复周期"
              rules={[{ required: true, message: '请选择重复周期' }]}
            >
              <Select 
                className="fast-input" 
                placeholder="选择重复周期"
                onChange={onRepeatTypeChange}
              >
                <Option value="daily">每天</Option>
                <Option value="weekly">每周</Option>
                <Option value="workdays">工作日</Option>
                <Option value="monthly">每月</Option>
                <Option value="yearly">每年</Option>
                <Option value="custom">自定义</Option>
              </Select>
            </Form.Item>
            
            {/* 自定义重复设置 */}
            {repeatType === 'custom' && (
              <Form.Item label="自定义重复">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Row gutter={8} align="middle">
                    <Col>
                      <span>每</span>
                    </Col>
                    <Col>
                      <InputNumber 
                        min={1} 
                        max={365} 
                        value={customRepeat.interval}
                        onChange={(value) => handleCustomRepeatChange('interval', value || 1)}
                      />
                    </Col>
                    <Col>
                      <Select 
                        value={customRepeat.unit}
                        onChange={(value) => handleCustomRepeatChange('unit', value)}
                        style={{ width: 80 }}
                      >
                        <Option value="day">天</Option>
                        <Option value="week">周</Option>
                        <Option value="month">月</Option>
                        <Option value="year">年</Option>
                      </Select>
                    </Col>
                  </Row>
                  
                  {customRepeat.unit === 'week' && (
                    <div>
                      <div style={{ marginBottom: 8 }}>选择星期：</div>
                      <Checkbox.Group 
                        value={customRepeat.weekdays}
                        onChange={(values) => handleCustomRepeatChange('weekdays', values)}
                      >
                        <Row>
                          <Col span={3}><Checkbox value={0}>周日</Checkbox></Col>
                          <Col span={3}><Checkbox value={1}>周一</Checkbox></Col>
                          <Col span={3}><Checkbox value={2}>周二</Checkbox></Col>
                          <Col span={3}><Checkbox value={3}>周三</Checkbox></Col>
                          <Col span={3}><Checkbox value={4}>周四</Checkbox></Col>
                          <Col span={3}><Checkbox value={5}>周五</Checkbox></Col>
                          <Col span={3}><Checkbox value={6}>周六</Checkbox></Col>
                        </Row>
                      </Checkbox.Group>
                    </div>
                  )}
                </Space>
              </Form.Item>
            )}
          </>
        ) : (
          // 短期目标和习惯养成显示日期
          <>
            <Form.Item
              name="startDate"
              label="开始日期"
            >
              <DatePicker className="fast-input" style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="deadline"
              label="截止日期"
            >
              <DatePicker className="fast-input" style={{ width: '100%' }} />
            </Form.Item>
          </>
        )}
      </Form>
    </Modal>
  );
});

export default GoalFormModal;
