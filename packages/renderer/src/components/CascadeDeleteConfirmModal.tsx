import React, { useState, useEffect } from 'react';
import {
  Modal,
  Typography,
  <PERSON>,
  Alert,
  Divider,
  List,
  Tag,
  Button,
  Spin,
  Progress,
  Collapse,
  Statistic,
  Row,
  Col
} from 'antd';
import {
  ExclamationCircleOutlined,
  DeleteOutlined,
  FolderOutlined,
  CheckCircleOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  BranchesOutlined,
  EditOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';

const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

export interface DeleteAnalysis {
  goalId: string;
  goalName: string;
  subGoalsCount: number;
  milestonesCount: number;
  tasksCount: number;
  pomodoroSessionsCount: number;
  decompositionSessionsCount: number;
  userModificationsCount: number;
  totalItemsCount: number;
  subGoals: Array<{
    id: string;
    name: string;
    milestonesCount: number;
    tasksCount: number;
  }>;
  milestones: Array<{
    id: string;
    name: string;
    tasksCount: number;
  }>;
  tasks: Array<{
    id: string;
    title: string;
    parentType: string;
    parentId: string;
  }>;
}

export interface DeleteResult {
  success: boolean;
  deletedItems: {
    goals: number;
    subGoals: number;
    milestones: number;
    tasks: number;
    pomodoroSessions: number;
    decompositionSessions: number;
    userModifications: number;
  };
  error?: string;
}

interface CascadeDeleteConfirmModalProps {
  visible: boolean;
  goalId: string | null;
  goalName: string;
  onConfirm: (goalId: string) => Promise<DeleteResult>;
  onCancel: () => void;
  onAnalyze: (goalId: string) => Promise<DeleteAnalysis>;
}

const CascadeDeleteConfirmModal: React.FC<CascadeDeleteConfirmModalProps> = ({
  visible,
  goalId,
  goalName,
  onConfirm,
  onCancel,
  onAnalyze
}) => {
  const { theme } = useTheme();
  const [loading, setLoading] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [analysis, setAnalysis] = useState<DeleteAnalysis | null>(null);
  const [deleteResult, setDeleteResult] = useState<DeleteResult | null>(null);
  const [step, setStep] = useState<'analyze' | 'confirm' | 'deleting' | 'result'>('analyze');

  useEffect(() => {
    if (visible && goalId) {
      setStep('analyze');
      setAnalysis(null);
      setDeleteResult(null);
      analyzeGoalDeletion();
    }
  }, [visible, goalId]);

  const analyzeGoalDeletion = async () => {
    if (!goalId) return;
    
    setLoading(true);
    try {
      const result = await onAnalyze(goalId);
      setAnalysis(result);
      setStep('confirm');
    } catch (error) {
      console.error('分析删除影响失败:', error);
      // 可以显示错误提示
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmDelete = async () => {
    if (!goalId) return;

    setStep('deleting');
    setDeleting(true);
    
    try {
      const result = await onConfirm(goalId);
      setDeleteResult(result);
      setStep('result');
      
      if (result.success) {
        // 延迟关闭模态框，让用户看到成功信息
        setTimeout(() => {
          onCancel();
        }, 2000);
      }
    } catch (error) {
      console.error('删除操作失败:', error);
      setDeleteResult({
        success: false,
        deletedItems: {
          goals: 0,
          subGoals: 0,
          milestones: 0,
          tasks: 0,
          pomodoroSessions: 0,
          decompositionSessions: 0,
          userModifications: 0
        },
        error: error instanceof Error ? error.message : '删除操作失败'
      });
      setStep('result');
    } finally {
      setDeleting(false);
    }
  };

  const renderAnalysisStep = () => (
    <div style={{ textAlign: 'center', padding: '40px 20px' }}>
      <Spin size="large" />
      <div style={{ marginTop: '16px' }}>
        <Text type="secondary">正在分析删除影响...</Text>
      </div>
    </div>
  );

  const renderConfirmStep = () => {
    if (!analysis) return null;

    return (
      <div>
        <Alert
          message="警告：此操作不可撤销"
          description="删除目标将永久移除所有相关数据，包括子目标、里程碑、任务和历史记录。"
          type="warning"
          icon={<ExclamationCircleOutlined />}
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <div style={{ marginBottom: '24px' }}>
          <Title level={4} style={{ color: theme.colors.text, marginBottom: '16px' }}>
            <DeleteOutlined style={{ marginRight: '8px', color: '#ff4d4f' }} />
            即将删除的内容
          </Title>
          
          <Row gutter={16} style={{ marginBottom: '16px' }}>
            <Col span={8}>
              <Statistic
                title="子目标"
                value={analysis.subGoalsCount}
                prefix={<FolderOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="里程碑"
                value={analysis.milestonesCount}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="任务"
                value={analysis.tasksCount}
                prefix={<FileTextOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Statistic
                title="番茄钟记录"
                value={analysis.pomodoroSessionsCount}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="AI分解记录"
                value={analysis.decompositionSessionsCount}
                prefix={<BranchesOutlined />}
                valueStyle={{ color: '#13c2c2' }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="修改记录"
                value={analysis.userModificationsCount}
                prefix={<EditOutlined />}
                valueStyle={{ color: '#eb2f96' }}
              />
            </Col>
          </Row>
        </div>

        <Divider />

        <div style={{ marginBottom: '16px' }}>
          <Text strong style={{ color: theme.colors.text }}>
            总计将删除 {analysis.totalItemsCount} 项内容
          </Text>
        </div>

        {(analysis.subGoals.length > 0 || analysis.milestones.length > 0 || analysis.tasks.length > 0) && (
          <Collapse ghost>
            {analysis.subGoals.length > 0 && (
              <Panel 
                header={`子目标详情 (${analysis.subGoals.length}个)`} 
                key="subgoals"
              >
                <List
                  size="small"
                  dataSource={analysis.subGoals}
                  renderItem={(subGoal) => (
                    <List.Item>
                      <div style={{ width: '100%' }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Text strong>{subGoal.name}</Text>
                          <Space>
                            <Tag color="blue">{subGoal.milestonesCount} 里程碑</Tag>
                            <Tag color="orange">{subGoal.tasksCount} 任务</Tag>
                          </Space>
                        </div>
                      </div>
                    </List.Item>
                  )}
                />
              </Panel>
            )}

            {analysis.milestones.length > 0 && (
              <Panel 
                header={`里程碑详情 (${analysis.milestones.length}个)`} 
                key="milestones"
              >
                <List
                  size="small"
                  dataSource={analysis.milestones}
                  renderItem={(milestone) => (
                    <List.Item>
                      <div style={{ width: '100%' }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Text>{milestone.name}</Text>
                          <Tag color="orange">{milestone.tasksCount} 任务</Tag>
                        </div>
                      </div>
                    </List.Item>
                  )}
                />
              </Panel>
            )}

            {analysis.tasks.length > 0 && (
              <Panel 
                header={`任务详情 (${analysis.tasks.length}个)`} 
                key="tasks"
              >
                <List
                  size="small"
                  dataSource={analysis.tasks.slice(0, 10)} // 只显示前10个
                  renderItem={(task) => (
                    <List.Item>
                      <Text>{task.title}</Text>
                    </List.Item>
                  )}
                />
                {analysis.tasks.length > 10 && (
                  <Text type="secondary">
                    还有 {analysis.tasks.length - 10} 个任务...
                  </Text>
                )}
              </Panel>
            )}
          </Collapse>
        )}
      </div>
    );
  };

  const renderDeletingStep = () => (
    <div style={{ textAlign: 'center', padding: '40px 20px' }}>
      <Spin size="large" />
      <div style={{ marginTop: '16px' }}>
        <Text type="secondary">正在删除数据...</Text>
      </div>
      <div style={{ marginTop: '8px' }}>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          请稍候，正在安全地删除所有相关数据
        </Text>
      </div>
    </div>
  );

  const renderResultStep = () => {
    if (!deleteResult) return null;

    if (deleteResult.success) {
      return (
        <div style={{ textAlign: 'center', padding: '40px 20px' }}>
          <CheckCircleOutlined 
            style={{ fontSize: '48px', color: '#52c41a', marginBottom: '16px' }} 
          />
          <Title level={4} style={{ color: '#52c41a', marginBottom: '16px' }}>
            删除成功
          </Title>
          <Paragraph style={{ color: theme.colors.textSecondary }}>
            已成功删除目标 "{goalName}" 及其所有相关数据
          </Paragraph>
          
          <div style={{ marginTop: '24px' }}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="删除的目标/子目标"
                  value={deleteResult.deletedItems.goals + deleteResult.deletedItems.subGoals}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="删除的任务"
                  value={deleteResult.deletedItems.tasks}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
            </Row>
          </div>
        </div>
      );
    } else {
      return (
        <div style={{ textAlign: 'center', padding: '40px 20px' }}>
          <ExclamationCircleOutlined 
            style={{ fontSize: '48px', color: '#ff4d4f', marginBottom: '16px' }} 
          />
          <Title level={4} style={{ color: '#ff4d4f', marginBottom: '16px' }}>
            删除失败
          </Title>
          <Alert
            message="删除操作失败"
            description={deleteResult.error || '未知错误，请重试'}
            type="error"
            showIcon
          />
        </div>
      );
    }
  };

  const getModalTitle = () => {
    switch (step) {
      case 'analyze':
        return '分析删除影响';
      case 'confirm':
        return `确认删除目标 "${goalName}"`;
      case 'deleting':
        return '正在删除';
      case 'result':
        return deleteResult?.success ? '删除完成' : '删除失败';
      default:
        return '删除目标';
    }
  };

  const getFooter = () => {
    switch (step) {
      case 'analyze':
      case 'deleting':
        return null;
      case 'confirm':
        return [
          <Button key="cancel" onClick={onCancel}>
            取消
          </Button>,
          <Button 
            key="confirm" 
            type="primary" 
            danger 
            onClick={handleConfirmDelete}
            loading={deleting}
          >
            确认删除
          </Button>
        ];
      case 'result':
        return [
          <Button key="close" type="primary" onClick={onCancel}>
            关闭
          </Button>
        ];
      default:
        return null;
    }
  };

  return (
    <Modal
      title={getModalTitle()}
      open={visible}
      onCancel={onCancel}
      footer={getFooter()}
      width={600}
      closable={step !== 'deleting'}
      maskClosable={false}
      destroyOnClose
      style={{
        background: theme.colors.cardBackground,
      }}
    >
      {step === 'analyze' && renderAnalysisStep()}
      {step === 'confirm' && renderConfirmStep()}
      {step === 'deleting' && renderDeletingStep()}
      {step === 'result' && renderResultStep()}
    </Modal>
  );
};

export default CascadeDeleteConfirmModal;
