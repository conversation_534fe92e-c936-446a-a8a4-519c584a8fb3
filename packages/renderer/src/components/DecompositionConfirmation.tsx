import React, { useState, useMemo, useEffect, ErrorInfo } from 'react';
import { Modal, Button, Space, Typography, Card, Alert, Divider, Checkbox, Steps, Spin, message } from 'antd';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SaveOutlined,
  EditOutlined,
  EyeOutlined,
  WarningOutlined
} from '@ant-design/icons';
import DecompositionResultTree from './DecompositionResultTree';
import DecompositionQualityHelper from './DecompositionQualityHelper';
import { DatabaseAPI } from '../services/api';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

// 错误边界组件
class DecompositionErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('DecompositionConfirmation错误边界捕获错误:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <Alert
          message="分解结果显示错误"
          description={
            <div>
              <p>分解结果组件渲染时出现错误，可能是数据格式问题。</p>
              <p>错误信息: {this.state.error?.message}</p>
              <Button
                type="primary"
                size="small"
                onClick={() => this.setState({ hasError: false })}
                style={{ marginTop: 8 }}
              >
                重试显示
              </Button>
            </div>
          }
          type="error"
          showIcon
        />
      );
    }

    return this.props.children;
  }
}

interface DecompositionConfirmationProps {
  visible: boolean;
  goalId: string;
  goalName: string;
  sessionId: string;
  originalGoal: {
    name: string;
    description: string;
    whyPower: string;
  };
  decompositionResult: any;
  onConfirm: (confirmed: boolean, modifications?: any) => void;
  onCancel: () => void;
  onEdit: () => void;
  loading?: boolean;
}

const DecompositionConfirmation: React.FC<DecompositionConfirmationProps> = ({
  visible,
  goalId,
  goalName,
  sessionId,
  originalGoal,
  decompositionResult,
  onConfirm,
  onCancel,
  onEdit,
  loading = false
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [userAgreements, setUserAgreements] = useState({
    reviewedStructure: false,
    acceptedTimeEstimate: false,
    understoodComplexity: false,
    readyToStart: false
  });

  // 添加数据验证和调试
  useEffect(() => {
    if (visible) {
      console.log('DecompositionConfirmation显示，数据验证:');
      console.log('- goalId:', goalId);
      console.log('- sessionId:', sessionId);
      console.log('- decompositionResult:', decompositionResult);

      if (!decompositionResult) {
        console.error('DecompositionConfirmation: decompositionResult为空');
      }
      if (!sessionId) {
        console.error('DecompositionConfirmation: sessionId为空');
      }
    }
  }, [visible, goalId, sessionId, decompositionResult]);

  // 计算统计信息
  const stats = useMemo(() => {
    if (!decompositionResult) return null;

    const subGoalsCount = decompositionResult.subGoals?.length || 0;
    const milestonesCount = decompositionResult.subGoals?.reduce(
      (sum: number, sg: any) => sum + (sg.milestones?.length || 0), 0
    ) || 0;
    const tasksCount = decompositionResult.subGoals?.reduce(
      (sum: number, sg: any) => sum + sg.milestones?.reduce(
        (mSum: number, m: any) => mSum + (m.tasks?.length || 0), 0
      ), 0
    ) || 0;

    return {
      subGoalsCount,
      milestonesCount,
      tasksCount,
      totalTime: decompositionResult.estimatedTotalTime || 0,
      complexity: decompositionResult.complexity || 'medium',
      confidence: decompositionResult.confidence || 0.7
    };
  }, [decompositionResult]);

  const handleAgreementChange = (key: keyof typeof userAgreements, checked: boolean) => {
    setUserAgreements(prev => ({
      ...prev,
      [key]: checked
    }));
  };

  const allAgreementsChecked = Object.values(userAgreements).every(Boolean);

  const handleConfirm = async () => {
    try {
      console.log('开始保存分解结果，sessionId:', sessionId);

      if (!sessionId) {
        throw new Error('缺少分解会话ID');
      }

      // 保存分解结果到数据库
      const result = await DatabaseAPI.saveDecompositionResult(sessionId, true);
      console.log('保存分解结果响应:', result);

      if (result.success) {
        message.success('分解结果已保存，开始执行计划！');
        console.log('分解结果保存成功，调用onConfirm');
        onConfirm(true);
      } else {
        throw new Error(result.error || '保存失败');
      }
    } catch (error) {
      console.error('保存分解结果失败:', error);
      message.error('保存失败: ' + (error as Error).message);
    }
  };

  const handleReject = async () => {
    try {
      // 标记为拒绝但不保存到数据库
      message.info('已拒绝分解结果');
      onConfirm(false);
    } catch (error) {
      console.error('拒绝分解结果失败:', error);
      message.error('操作失败: ' + (error as Error).message);
    }
  };

  const steps = [
    {
      title: '查看分解结果',
      description: '检查AI生成的目标分解结构',
      icon: <EyeOutlined />
    },
    {
      title: '确认细节',
      description: '确认时间估算和复杂度评估',
      icon: <ExclamationCircleOutlined />
    },
    {
      title: '最终确认',
      description: '确认接受分解结果并开始执行',
      icon: <CheckCircleOutlined />
    }
  ];

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div>
            <Alert
              message="请仔细查看AI生成的分解结果"
              description="检查子目标、里程碑和任务是否符合您的期望。如需修改，可以点击编辑按钮。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            {/* 分解质量检查 */}
            <DecompositionErrorBoundary>
              <DecompositionQualityHelper
                decompositionResult={decompositionResult}
                compact={true}
              />
            </DecompositionErrorBoundary>

            {decompositionResult && (
              <DecompositionErrorBoundary>
                <DecompositionResultTree
                  result={decompositionResult}
                  goalName={goalName}
                  editable={false}
                />
              </DecompositionErrorBoundary>
            )}

            <div style={{ textAlign: 'center', marginTop: 16 }}>
              <Space>
                <Button icon={<EditOutlined />} onClick={onEdit}>
                  编辑分解结果
                </Button>
                <Button type="primary" onClick={() => setCurrentStep(1)}>
                  继续确认
                </Button>
              </Space>
            </div>
          </div>
        );

      case 1:
        return (
          <div>
            <Alert
              message="请确认以下评估信息"
              description="AI对您的目标进行了分析，请确认这些评估是否合理。"
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />

            {stats && (
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 16, marginBottom: 16 }}>
                <Card size="small">
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                      {stats.subGoalsCount}
                    </div>
                    <div style={{ color: '#666' }}>子目标</div>
                  </div>
                </Card>

                <Card size="small">
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                      {stats.milestonesCount}
                    </div>
                    <div style={{ color: '#666' }}>里程碑</div>
                  </div>
                </Card>

                <Card size="small">
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#faad14' }}>
                      {stats.tasksCount}
                    </div>
                    <div style={{ color: '#666' }}>具体任务</div>
                  </div>
                </Card>

                <Card size="small">
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#722ed1' }}>
                      {stats.totalTime}h
                    </div>
                    <div style={{ color: '#666' }}>预估总时长</div>
                  </div>
                </Card>
              </div>
            )}

            <Card size="small" style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <Text strong>复杂度评估: </Text>
                  <Text style={{ 
                    color: stats?.complexity === 'high' ? '#ff4d4f' : 
                          stats?.complexity === 'medium' ? '#faad14' : '#52c41a'
                  }}>
                    {stats?.complexity === 'high' ? '高复杂度' : 
                     stats?.complexity === 'medium' ? '中等复杂度' : '低复杂度'}
                  </Text>
                </div>
                <div>
                  <Text strong>AI信心度: </Text>
                  <Text style={{ 
                    color: (stats?.confidence || 0) >= 0.8 ? '#52c41a' : 
                          (stats?.confidence || 0) >= 0.6 ? '#faad14' : '#ff4d4f'
                  }}>
                    {((stats?.confidence || 0) * 100).toFixed(0)}%
                  </Text>
                </div>
              </div>
            </Card>

            <div style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 8 }}>
                <Checkbox
                  checked={userAgreements.reviewedStructure}
                  onChange={(e) => handleAgreementChange('reviewedStructure', e.target.checked)}
                >
                  我已仔细查看了分解结构，认为合理可行
                </Checkbox>
              </div>
              <div style={{ marginBottom: 8 }}>
                <Checkbox
                  checked={userAgreements.acceptedTimeEstimate}
                  onChange={(e) => handleAgreementChange('acceptedTimeEstimate', e.target.checked)}
                >
                  我接受AI的时间估算，了解实际执行可能有差异
                </Checkbox>
              </div>
              <div style={{ marginBottom: 8 }}>
                <Checkbox
                  checked={userAgreements.understoodComplexity}
                  onChange={(e) => handleAgreementChange('understoodComplexity', e.target.checked)}
                >
                  我理解目标的复杂度，做好了相应的心理准备
                </Checkbox>
              </div>
            </div>

            <div style={{ textAlign: 'center' }}>
              <Space>
                <Button onClick={() => setCurrentStep(0)}>
                  返回查看
                </Button>
                <Button 
                  type="primary" 
                  disabled={!userAgreements.reviewedStructure || !userAgreements.acceptedTimeEstimate || !userAgreements.understoodComplexity}
                  onClick={() => setCurrentStep(2)}
                >
                  确认无误
                </Button>
              </Space>
            </div>
          </div>
        );

      case 2:
        return (
          <div style={{ textAlign: 'center' }}>
            <div style={{ marginBottom: 24 }}>
              <CheckCircleOutlined style={{ fontSize: 64, color: '#52c41a', marginBottom: 16 }} />
              <Title level={3}>准备开始执行</Title>
              <Paragraph>
                您即将开始执行目标"{goalName}"的分解计划。
                <br />
                确认后，系统将为您创建所有子目标、里程碑和任务。
              </Paragraph>
            </div>

            <Alert
              message="最后确认"
              description="确认后将无法撤销，但您可以随时修改具体的任务内容。"
              type="warning"
              showIcon
              style={{ marginBottom: 24 }}
            />

            <div style={{ marginBottom: 24 }}>
              <Checkbox
                checked={userAgreements.readyToStart}
                onChange={(e) => handleAgreementChange('readyToStart', e.target.checked)}
              >
                我已准备好开始执行这个目标，确认接受AI分解结果
              </Checkbox>
            </div>

            <Space size="large">
              <Button size="large" onClick={() => setCurrentStep(1)}>
                返回修改
              </Button>
              <Button 
                type="primary" 
                size="large" 
                icon={<SaveOutlined />}
                disabled={!userAgreements.readyToStart}
                loading={loading}
                onClick={handleConfirm}
              >
                确认并开始执行
              </Button>
            </Space>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Modal
      title={
        <div>
          <Title level={4} style={{ margin: 0 }}>
            确认AI分解结果
          </Title>
          <Text type="secondary">目标: {goalName}</Text>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={800}
      style={{ top: 20 }}
      destroyOnClose
    >
      <div style={{ marginBottom: 24 }}>
        <Steps current={currentStep} items={steps} />
      </div>

      <Divider />

      {renderStepContent()}

      <Divider />

      <div style={{ textAlign: 'center' }}>
        <Space>
          <Button onClick={handleReject}>
            拒绝分解结果
          </Button>
          <Button onClick={onCancel}>
            稍后决定
          </Button>
        </Space>
      </div>
    </Modal>
  );
};

export default DecompositionConfirmation;
