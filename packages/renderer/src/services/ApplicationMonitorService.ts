import { focusMonitorService } from './FocusMonitorService';

// 应用信息接口
export interface ApplicationInfo {
  id: string;
  name: string;
  bundleId?: string; // macOS应用包标识符
  executable?: string; // Windows可执行文件名
  windowTitle?: string; // 当前窗口标题
  isActive: boolean;
  lastActiveTime: Date;
  category?: 'productivity' | 'social' | 'entertainment' | 'development' | 'other';
}

// 网站访问信息接口
export interface WebsiteInfo {
  id: string;
  url: string;
  domain: string;
  title: string;
  isActive: boolean;
  lastActiveTime: Date;
  tabId?: number;
  category?: 'work' | 'social' | 'entertainment' | 'news' | 'shopping' | 'other';
}

// 活动记录接口
export interface ActivityRecord {
  id: string;
  timestamp: Date;
  type: 'app' | 'website';
  target: ApplicationInfo | WebsiteInfo;
  duration: number; // 毫秒
  isBlacklisted: boolean;
  isWhitelisted: boolean;
}

class ApplicationMonitorService {
  private isMonitoring: boolean = false;
  private monitorTimer?: NodeJS.Timeout;
  private currentActiveApp?: ApplicationInfo;
  private currentActiveWebsite?: WebsiteInfo;
  private activityHistory: ActivityRecord[] = [];
  private lastCheckTime: Date = new Date();
  
  // 监控间隔（毫秒）
  private readonly MONITOR_INTERVAL = 1000; // 1秒检查一次
  
  // 事件监听器
  private listeners: {
    onAppChanged?: (app: ApplicationInfo) => void;
    onWebsiteChanged?: (website: WebsiteInfo) => void;
    onBlacklistAccess?: (target: ApplicationInfo | WebsiteInfo) => void;
  } = {};

  // 模拟浏览器扩展数据（用于测试）
  private mockBrowserTabs: { id: number; url: string; title: string; active: boolean }[] = [];

  constructor() {
    this.loadActivityHistory();
  }

  // 开始监控
  public startMonitoring(): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.lastCheckTime = new Date();
    
    // 启动定期检查
    this.monitorTimer = setInterval(() => {
      this.checkActiveApplication();
      this.checkActiveWebsite();
    }, this.MONITOR_INTERVAL);

    console.log('Application monitoring started');
  }

  // 停止监控
  public stopMonitoring(): void {
    if (!this.isMonitoring) return;
    
    this.isMonitoring = false;
    
    if (this.monitorTimer) {
      clearInterval(this.monitorTimer);
      this.monitorTimer = undefined;
    }

    this.saveActivityHistory();
    console.log('Application monitoring stopped');
  }

  // 检查当前活跃应用
  private async checkActiveApplication(): Promise<void> {
    try {
      // 通过Electron API获取活跃应用信息
      const activeApp = await this.getCurrentActiveApplication();
      
      if (!activeApp) return;

      // 如果应用发生变化
      if (!this.currentActiveApp || this.currentActiveApp.id !== activeApp.id) {
        // 记录上一个应用的活动时长
        if (this.currentActiveApp) {
          await this.recordActivity('app', this.currentActiveApp);
        }

        // 更新当前活跃应用
        this.currentActiveApp = activeApp;
        this.lastCheckTime = new Date();

        // 通知监听器
        if (this.listeners.onAppChanged) {
          this.listeners.onAppChanged(activeApp);
        }

        // 检查是否在黑名单中
        await this.checkBlacklistAccess('app', activeApp);
      }
    } catch (error) {
      console.error('检查活跃应用失败:', error);
    }
  }

  // 检查当前活跃网站
  private async checkActiveWebsite(): Promise<void> {
    try {
      // 通过浏览器扩展或API获取活跃网站信息
      const activeWebsite = await this.fetchCurrentActiveWebsite();
      
      if (!activeWebsite) return;

      // 如果网站发生变化
      if (!this.currentActiveWebsite || this.currentActiveWebsite.id !== activeWebsite.id) {
        // 记录上一个网站的活动时长
        if (this.currentActiveWebsite) {
          await this.recordActivity('website', this.currentActiveWebsite);
        }

        // 更新当前活跃网站
        this.currentActiveWebsite = activeWebsite;
        this.lastCheckTime = new Date();

        // 通知监听器
        if (this.listeners.onWebsiteChanged) {
          this.listeners.onWebsiteChanged(activeWebsite);
        }

        // 检查是否在黑名单中
        await this.checkBlacklistAccess('website', activeWebsite);
      }
    } catch (error) {
      console.error('检查活跃网站失败:', error);
    }
  }

  // 获取当前活跃应用（实际实现需要调用Electron API）
  private async getCurrentActiveApplication(): Promise<ApplicationInfo | null> {
    try {
      // 模拟API调用 - 实际实现需要在主进程中获取系统信息
      if (window.electronAPI?.getActiveApplication) {
        const appInfo = await window.electronAPI.getActiveApplication();
        
        if (appInfo) {
          return {
            id: appInfo.bundleId || appInfo.executable || appInfo.name,
            name: appInfo.name,
            bundleId: appInfo.bundleId,
            executable: appInfo.executable,
            windowTitle: appInfo.windowTitle,
            isActive: true,
            lastActiveTime: new Date(),
            category: this.categorizeApplication(appInfo.name)
          };
        }
      }
      
      // 降级方案：使用document.hasFocus()检测浏览器活跃状态
      if (document.hasFocus()) {
        return {
          id: 'browser',
          name: 'Browser',
          isActive: true,
          lastActiveTime: new Date(),
          category: 'other'
        };
      }
      
      return null;
    } catch (error) {
      console.error('获取活跃应用失败:', error);
      return null;
    }
  }

  // 获取当前活跃网站
  private async fetchCurrentActiveWebsite(): Promise<WebsiteInfo | null> {
    try {
      // 优先使用模拟数据（用于测试）
      const mockTab = this.getMockActiveTab();
      if (mockTab && mockTab.url.startsWith('http')) {
        return {
          id: mockTab.id.toString(),
          url: mockTab.url,
          domain: new URL(mockTab.url).hostname,
          title: mockTab.title,
          isActive: true,
          lastActiveTime: new Date(),
          tabId: mockTab.id,
          category: this.categorizeWebsite(new URL(mockTab.url).hostname)
        };
      }

      // 使用浏览器扩展获取活跃标签页信息
      if (window.focusShieldExtension?.getActiveTab) {
        const tabInfo = await window.focusShieldExtension.getActiveTab();
        
        if (tabInfo && tabInfo.url && tabInfo.url.startsWith('http')) {
          return {
            id: tabInfo.id.toString(),
            url: tabInfo.url,
            domain: new URL(tabInfo.url).hostname,
            title: tabInfo.title,
            isActive: true,
            lastActiveTime: new Date(),
            tabId: tabInfo.id,
            category: this.categorizeWebsite(new URL(tabInfo.url).hostname)
          };
        }
      }

      // 尝试通过Electron API获取活跃浏览器信息
      if (window.electronAPI?.getActiveBrowserTab) {
        const browserInfo = await window.electronAPI.getActiveBrowserTab();
        
        if (browserInfo && browserInfo.url && browserInfo.url.startsWith('http')) {
          return {
            id: 'browser-tab-' + Date.now(),
            url: browserInfo.url,
            domain: new URL(browserInfo.url).hostname,
            title: browserInfo.title || new URL(browserInfo.url).hostname,
            isActive: true,
            lastActiveTime: new Date(),
            category: this.categorizeWebsite(new URL(browserInfo.url).hostname)
          };
        }
      }

      // 降级方案：检测当前页面（仅当Focus应用在前台时）
      if (document.hasFocus()) {
        const url = window.location.href;
        
        // 排除Focus应用本身的页面
        if (url.includes('localhost') || url.includes('file://') || url.includes('focusos')) {
          return null;
        }
        
        const domain = new URL(url).hostname;
        
        return {
          id: domain + '-' + Date.now(),
          url,
          domain,
          title: document.title || domain,
          isActive: true,
          lastActiveTime: new Date(),
          category: this.categorizeWebsite(domain)
        };
      }
      
      return null;
    } catch (error) {
      console.error('获取活跃网站失败:', error);
      return null;
    }
  }

  // 应用分类
  private categorizeApplication(appName: string): ApplicationInfo['category'] {
    const name = appName.toLowerCase();
    
    if (name.includes('code') || name.includes('idea') || name.includes('studio')) {
      return 'development';
    }
    if (name.includes('slack') || name.includes('teams') || name.includes('discord')) {
      return 'social';
    }
    if (name.includes('music') || name.includes('video') || name.includes('game')) {
      return 'entertainment';
    }
    if (name.includes('word') || name.includes('excel') || name.includes('notes')) {
      return 'productivity';
    }
    
    return 'other';
  }

  // 网站分类
  private categorizeWebsite(domain: string): WebsiteInfo['category'] {
    const socialDomains = ['facebook.com', 'twitter.com', 'x.com', 'instagram.com', 'linkedin.com', 'weibo.com', 'tiktok.com'];
    const entertainmentDomains = ['youtube.com', 'netflix.com', 'bilibili.com', 'douyin.com', 'twitch.tv'];
    const newsDomains = ['news.com', 'bbc.com', 'cnn.com', 'xinhua.com', 'reddit.com'];
    const shoppingDomains = ['amazon.com', 'taobao.com', 'jd.com', 'ebay.com'];
    const workDomains = ['github.com', 'stackoverflow.com', 'docs.google.com', 'notion.so'];

    if (socialDomains.some(d => domain.includes(d))) return 'social';
    if (entertainmentDomains.some(d => domain.includes(d))) return 'entertainment';
    if (newsDomains.some(d => domain.includes(d))) return 'news';
    if (shoppingDomains.some(d => domain.includes(d))) return 'shopping';
    if (workDomains.some(d => domain.includes(d))) return 'work';

    return 'other';
  }

  // 检查黑名单访问 - 优化错误处理和防抖
  private lastBlacklistCheck = new Map<string, number>();
  private async checkBlacklistAccess(type: 'app' | 'website', target: ApplicationInfo | WebsiteInfo): Promise<void> {
    try {
      const targetKey = `${type}-${target.name || (target as WebsiteInfo).domain}`;
      const now = Date.now();
      const lastCheck = this.lastBlacklistCheck.get(targetKey) || 0;
      
      // 防抖：同一目标在5秒内只检查一次
      if (now - lastCheck < 5000) {
        return;
      }
      
      this.lastBlacklistCheck.set(targetKey, now);
      
      // 从黑名单管理服务检查
      const blacklistManager = await import('./BlacklistManagerService');
      const isBlacklisted = await blacklistManager.blacklistManagerService.isBlacklisted(type, target);
      
      if (isBlacklisted) {
        // 触发黑名单访问事件
        if (this.listeners.onBlacklistAccess) {
          this.listeners.onBlacklistAccess(target);
        }

        // 记录分心行为
        focusMonitorService.recordDistraction({
          type: 'app-switch',
          description: `访问黑名单${type === 'app' ? '应用' : '网站'}: ${target.name || (target as WebsiteInfo).domain}`,
          severity: 'medium',
          category: '黑名单访问'
        });
      }
    } catch (error) {
      console.error('检查黑名单失败:', error);
      // 静默处理错误，不影响正常监控流程
    }
  }

  // 记录活动
  private async recordActivity(type: 'app' | 'website', target: ApplicationInfo | WebsiteInfo): Promise<void> {
    const now = new Date();
    const duration = now.getTime() - this.lastCheckTime.getTime();
    
    // 只记录超过3秒的活动
    if (duration < 3000) return;

    const record: ActivityRecord = {
      id: `activity-${Date.now()}`,
      timestamp: this.lastCheckTime,
      type,
      target,
      duration,
      isBlacklisted: false, // 将由黑名单管理器更新
      isWhitelisted: false  // 将由黑名单管理器更新
    };

    this.activityHistory.push(record);

    // 保持历史记录在合理范围内（最多1000条）
    if (this.activityHistory.length > 1000) {
      this.activityHistory = this.activityHistory.slice(-1000);
    }
  }

  // 事件监听器设置
  public on(event: 'appChanged', callback: (app: ApplicationInfo) => void): void;
  public on(event: 'websiteChanged', callback: (website: WebsiteInfo) => void): void;
  public on(event: 'blacklistAccess', callback: (target: ApplicationInfo | WebsiteInfo) => void): void;
  public on(event: string, callback: any): void {
    this.listeners[`on${event.charAt(0).toUpperCase() + event.slice(1)}` as keyof typeof this.listeners] = callback;
  }

  // 获取活动历史
  public getActivityHistory(hours: number = 24): ActivityRecord[] {
    const cutoff = new Date();
    cutoff.setHours(cutoff.getHours() - hours);
    
    return this.activityHistory.filter(record => record.timestamp >= cutoff);
  }

  // 获取当前活跃目标
  public getCurrentActiveApp(): ApplicationInfo | undefined {
    return this.currentActiveApp;
  }

  public getCurrentActiveWebsite(): WebsiteInfo | undefined {
    return this.currentActiveWebsite;
  }

  // 数据持久化
  private saveActivityHistory(): void {
    try {
      // 只保存最近24小时的数据
      const dayAgo = new Date();
      dayAgo.setDate(dayAgo.getDate() - 1);
      
      const recentHistory = this.activityHistory.filter(record => record.timestamp >= dayAgo);
      
      localStorage.setItem('focusOS_activity_history', JSON.stringify(recentHistory));
    } catch (error) {
      console.error('保存活动历史失败:', error);
    }
  }

  private loadActivityHistory(): void {
    try {
      const saved = localStorage.getItem('focusOS_activity_history');
      if (saved) {
        this.activityHistory = JSON.parse(saved).map((record: any) => ({
          ...record,
          timestamp: new Date(record.timestamp),
          target: {
            ...record.target,
            lastActiveTime: new Date(record.target.lastActiveTime)
          }
        }));
      }
    } catch (error) {
      console.error('加载活动历史失败:', error);
      this.activityHistory = [];
    }
  }

  // 模拟访问网站（用于测试）
  public simulateWebsiteVisit(url: string, title?: string): void {
    try {
      const mockTab = {
        id: Date.now(),
        url,
        title: title || new URL(url).hostname,
        active: true
      };
      
      // 清除之前的活跃状态
      this.mockBrowserTabs.forEach(tab => tab.active = false);
      
      // 添加新的活跃标签页
      this.mockBrowserTabs.push(mockTab);
      
      console.log(`模拟访问网站: ${url}`);
      
      // 立即触发网站检查
      this.checkActiveWebsite();
      
    } catch (error) {
      console.error('模拟网站访问失败:', error);
    }
  }

  // 获取模拟的活跃标签页（用于测试）
  private getMockActiveTab(): { id: number; url: string; title: string } | null {
    const activeTab = this.mockBrowserTabs.find(tab => tab.active);
    return activeTab || null;
  }

  // 测试方法：模拟访问x.com
  public async testXcomAccess(): Promise<void> {
    console.log('开始测试 x.com 访问监控...');

    // 模拟访问x.com
    this.simulateWebsiteVisit('https://x.com', 'X (formerly Twitter)');

    // 等待一下让检测逻辑执行
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('x.com 访问测试完成');
  }

  // 测试方法：获取当前违规记录数量
  public getViolationCount(): number {
    const blacklistManager = require('./BlacklistManagerService');
    const violations = blacklistManager.blacklistManagerService.getViolationHistory(1);
    return violations.length;
  }

  // 清理资源
  public destroy(): void {
    this.stopMonitoring();
    this.saveActivityHistory();
    this.lastBlacklistCheck.clear();
  }
}

// 单例实例
export const applicationMonitorService = new ApplicationMonitorService();
export default ApplicationMonitorService;